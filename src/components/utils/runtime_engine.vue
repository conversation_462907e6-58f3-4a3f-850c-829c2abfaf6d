<template>
	<div
		class="w-full h-full flex gap-2"
		:class="{
			'flex-row': prop.view_mode == 'left-right',
			'flex-col': prop.view_mode == 'default',
		}"
	>
		<div
			v-if="!hasOutputNode"
			class="alert alert-error mt-2 flex flex-nowrap rounded-md shadow-sm"
		>
			<svg
				xmlns="http://www.w3.org/2000/svg"
				class="stroke-current flex-shrink-0 h-6 w-6"
				fill="none"
				viewBox="0 0 24 24"
			>
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
				/>
			</svg>
			<span>{{ t("warnning-no-output-node") }}</span>
		</div>

		<div class="flex-1">
			<div
				v-if="Object.keys(input).length > 0"
				class="p-2 border border-gray-200 rounded-md flex flex-wrap mt-3 space-y-4 pb-4 shadow-sm"
			>
				<div
					v-for="key in Object.keys(input)"
					:key="key"
					class="px-2 flex-shrink-1 w-full"
					:class="{
						'border border-1 border-red-500 mt-1 rounded-md':
							invalidInputs.includes(key),
					}"
				>
					<label class="label">{{ key }}:</label>
					<input
						v-if="inputMeta[key].type == 'text'"
						class="input input-bordered w-full input-md"
						v-model="input[key]"
					/>
					<textarea
						v-if="inputMeta[key].type == 'longtext'"
						class="textarea textarea-bordered w-full leading-4"
						rows="10"
						v-model="input[key]"
					/>
					<select
						v-if="inputMeta[key].type == 'select'"
						class="select select-bordered w-full"
						v-model="input[key]"
						:class="{ 'select-error': invalidInputs.includes(key) }"
					>
						<option value="" disabled selected>{{ t("pls-select") }}</option>
						<option
							v-for="option in inputMeta[key].options"
							:key="option.value"
							:value="option.value"
						>
							{{ option.value }}
						</option>
					</select>
					<image-upload
						v-if="inputMeta[key].type == 'image'"
						v-model="input[key]"
						:max-count="inputMeta[key].default || 1"
						@error="(error) => handleImageError(key, error)"
						@update:modelValue="() => clearImageError(key)"
					/>
					<div
						v-if="inputMeta[key].type == 'radio'"
						class="flex flex-wrap gap-2 rounded-md"
					>
						<radio-input v-model="input[key]" :options="inputMeta[key].options" />
					</div>
					<div
						v-if="inputMeta[key].type == 'checkbox'"
						class="flex flex-wrap gap-2 rounded-md"
					>
						<checkbox-input
							v-model="input[key]"
							:options="inputMeta[key].options"
						/>
					</div>
					<p
						v-if="invalidInputs.includes(key)"
						class="text-red-500 text-sm my-2 flex items-center"
					>
						{{ getErrorMessage(key) }}
					</p>
				</div>
			</div>
			<div class="text-center mt-3">
				<button
					class="btn btn-gradient-primary mt-3 w-1/2 transition-all"
					:class="{
						'btn-disabled':
							isRunning ||
							!hasOutputNode ||
							(isReconnecting && prop.mode === 'run'),
					}"
					@click="start"
				>
					<RocketLaunchIcon class="w-4 h-4 mr-1" />
					{{ t("start-exec") }}
				</button>

				<!-- 重连状态显示 (只在run模式下显示) -->
				<div
					v-if="isReconnecting && prop.mode === 'run'"
					class="mt-2 text-sm text-blue-600 flex items-center justify-center"
				>
					<ArrowPathIcon class="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-600" />
					{{ t("reconnecting") }} ({{ reconnectAttempts }}/{{
						maxReconnectAttempts
					}})
				</div>
			</div>
			<div class="log-container relative overflow-hidden mt-3">
				<div
					ref="logRef"
					class="mt-2 bg-slate-700 rounded-lg text-white text-xs overflow-y-auto transition-all duration-300"
					:class="{
						'h-[130px] p-3': isLogExpanded,
						'h-0 px-3': !isLogExpanded,
					}"
					:style="{
						opacity: isLogExpanded ? 1 : 0,
					}"
				>
					<div
						v-for="(logItem, index) in logItems"
						:key="index"
						class="fade-in-item"
						:style="{
							marginLeft: (logItem.deep || 0) * 20 + 'px',
						}"
					>
						<template v-if="logItem.type === 'node'">
							'{{ logItem.name }}' {{ logItem.action }}
							<template v-if="logItem.duration"
								>{{ t("duration") }}: {{ logItem.duration }}
								{{ t("seconds") }}</template
							>
						</template>
						<span v-else-if="logItem.type === 'error'" class="text-red-400">
							{{ t("error") }}: '{{ logItem.message }}'
						</span>
						<div
							v-else-if="logItem.type === 'agent'"
							class="whitespace-pre-wrap p-2 rounded-md border-l-2 my-1"
						>
							<div class="flex items-center mb-1 text-xs">
								<BoltIcon class="h-3 w-3 mr-1" />
								Agent
							</div>
							<div class="text-xs">{{ logItem.content }}</div>
						</div>
						<div v-else-if="logItem.type === 'stream'" class="whitespace-pre-wrap">
							<template v-if="logItem.reasoning">
								<div
									class="border-l-2 border-emerald-400 pl-3 my-2 opacity-90"
								>
									<div
										class="flex items-center text-emerald-400 mb-1 text-xs"
									>
										<LightBulbIcon class="h-4 w-4 mr-1" />
										<span>{{ t("ai-reasoning") }}</span>
									</div>
									<div class="text-gray-300 text-xs leading-relaxed">
										<!-- Consolidated reasoning content -->
										<span v-if="logItem.consolidatedReasoning">
											{{ logItem.consolidatedReasoning }}</span
										>
										<!-- New reasoning characters with animation -->
										<span
											v-for="(
												char, charIndex
											) in logItem.pendingReasoningChars"
											:key="'reasoning-pending-' + charIndex"
											class="fade-in-char"
										>
											{{ char }}
										</span>
									</div>
								</div>
							</template>
							<!-- Consolidated content -->
							<span v-if="logItem.consolidatedContent">
								{{ logItem.consolidatedContent }}</span
							>
							<!-- New characters with animation -->
							<span
								v-for="(char, charIndex) in logItem.pendingChars"
								:key="'pending-' + charIndex"
								class="fade-in-char"
							>
								{{ char }}
							</span>
						</div>
					</div>
				</div>

				<div v-if="logItems.length > 0" class="text-center h-6 mt-2">
					<button @click="toggleLogExpand" class="btn btn-xs btn-ghost">
						{{ isLogExpanded ? t("collapse") : t("expand") }}
					</button>
				</div>
			</div>
		</div>
		<div class="flex-1">
			<!-- 修改后的分隔线部分 -->
			<div class="divider text-base-300 text-xs">
				<div class="flex items-center justify-between">
					<div>
						{{ t("output") }}
						<span v-if="running_time"
							>{{ t("running_time") }}: {{ running_time.toFixed(2) }}
							{{ t("seconds") }}</span
						>
					</div>
					<button
						v-if="output"
						@click="copyOutput"
						class="ml-2 p-1 hover:bg-gray-100 rounded-md text-base-500 hover:text-gray-700 transition-colors"
						:title="t('copy')"
					>
						<ClipboardDocumentIcon class="h-4 w-4" />
					</button>
				</div>
			</div>

			<div class="mt-3 rounded-lg p-3">
				<component_wait>
					<template v-if="output">
						<viewer class="w-full h-full" :output="output"></viewer>
					</template>
					<template v-else>
						<div
							class="flex flex-col items-center justify-center py-8 text-center"
						>
							<LightBulbIcon class="h-12 w-12 text-gray-300 mb-4" />
							<p class="text-gray-500 text-base mb-1">
								{{ t("no_results_yet") }}
							</p>
							<p class="text-gray-400 text-sm">{{ t("click_start_to_run") }}</p>
						</div>
					</template>
				</component_wait>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
	import { nextTick, onDeactivated, ref, watch, defineAsyncComponent } from "vue";
	import type { Node } from "@vue-flow/core";
	import { Error, Success } from "@/utils/notify";
	import { ProjectRuntime, ProjectDebug } from "@/api/runtime";
	import { getWorkflowLog } from "@/api/logs";
	import {
		RocketLaunchIcon,
		ArrowPathIcon,
		BoltIcon,
		LightBulbIcon,
		ClipboardDocumentIcon,
	} from "@heroicons/vue/24/outline";
	import component_wait from "@/components/utils/component_wait.vue";
	import ImageUpload from "@/components/runtime/image_upload.vue";
	import CheckboxInput from "@/components/runtime/checkbox_input.vue";
	import RadioInput from "@/components/runtime/radio_input.vue";
	import {
		parseWorkflowInput,
		validateInputs as validateInputsUtil,
		processImages as processImagesUtil,
		prepareInputForAPI,
	} from "@/utils/input_processor";
	import { useI18n } from "vue-i18n";
	const { t } = useI18n();
	const viewer = defineAsyncComponent(() => import("@/components/viewer.vue"));
	const prop = defineProps({
		data: {
			type: Object,
			default: () => ({}),
			required: false,
		},
		mode: {
			default: "debug",
			type: String,
			required: false,
		},
		project_id: {
			type: String,
			default: () => "",
			required: false,
		},
		view_mode: {
			type: String,
			default: () => "default", // default, left-right
			required: false,
		},
	});
	const output = ref("");
	const isRunning = ref(false);
	const input = ref<Record<string, any>>({});
	const inputMeta = ref<Record<string, any>>({});
	const logRef = ref<HTMLElement | null>(null);
	const logGetError = ref(false);
	const nodeMap = ref<Record<string, nodeNameMapValue>>({});
	let controller: AbortController | null = null;
	let currData = {};
	let running_time = ref<null | number>(null);
	const isLogExpanded = ref(false);
	const nodeStartTimes: Record<string, number> = {};
	const invalidInputs = ref<string[]>([]);
	const errorMessages = ref<Record<string, string>>({});

	// 断线重连相关状态
	const currentLogId = ref<string | null>(null);
	const reconnectTimer = ref<NodeJS.Timeout | null>(null);
	const isReconnecting = ref(false);
	const reconnectAttempts = ref(0);
	const maxReconnectAttempts = 10; // 最大重连次数
	const reconnectInterval = 3000; // 重连间隔（毫秒）

	interface LogItem {
		type: "node" | "error" | "stream" | "agent";
		content?: string;
		name?: string;
		action?: string;
		duration?: string;
		message?: string;
		deep?: number;
		reasoning?: string;
		// For optimized rendering
		consolidatedContent?: string;
		pendingChars?: string;
		lastConsolidationTime?: number;
		// For reasoning optimization
		consolidatedReasoning?: string;
		pendingReasoningChars?: string;
	}

	const logItems = ref<LogItem[]>([]);

	// 断线重连核心函数
	const startReconnectPolling = (logId: string) => {
		if (!logId || isReconnecting.value) return;

		currentLogId.value = logId;
		isReconnecting.value = true;
		reconnectAttempts.value = 0;

		const pollLog = async () => {
			try {
				const logData = await getWorkflowLog(logId);
				console.log("重连轮询结果:", logData);
				debugger;
				// 检查log是否存在且有内容
				if (logData && logData.output) {
					let outputData;
					try {
						outputData = JSON.parse(logData.output);
					} catch (e) {
						// 如果解析失败，直接使用原始output
						outputData = { output: logData.output };
					}

					// 如果当前output为空且log有内容，则覆盖结果
					if (!output.value && (outputData.output || logData.output)) {
						output.value = outputData.output || logData.output;
						running_time.value =
							outputData.total_time || logData.total_time || null;

						// 添加重连成功的日志
						logItems.value.push({
							type: "node",
							name: t("reconnect-success"),
							action: t("recovered-from-log"),
						});

						// 停止重连
						stopReconnectPolling();
						handleRunComplete();
						return;
					}
				}

				reconnectAttempts.value++;

				// 如果达到最大重连次数，停止重连
				if (reconnectAttempts.value >= maxReconnectAttempts) {
					logItems.value.push({
						type: "error",
						message: t("reconnect-failed-max-attempts"),
					});
					stopReconnectPolling();
					handleRunComplete();
					return;
				}

				// 继续下一次轮询
				reconnectTimer.value = setTimeout(pollLog, reconnectInterval);
			} catch (error) {
				console.error("重连轮询失败:", error);
				reconnectAttempts.value++;

				if (reconnectAttempts.value >= maxReconnectAttempts) {
					logItems.value.push({
						type: "error",
						message: t("reconnect-failed-error") + error,
					});
					stopReconnectPolling();
					handleRunComplete();
					return;
				}

				// 继续下一次轮询
				reconnectTimer.value = setTimeout(pollLog, reconnectInterval);
			}
		};

		// 开始第一次轮询
		reconnectTimer.value = setTimeout(pollLog, reconnectInterval);
	};

	const stopReconnectPolling = () => {
		if (reconnectTimer.value) {
			clearTimeout(reconnectTimer.value);
			reconnectTimer.value = null;
		}
		isReconnecting.value = false;
		currentLogId.value = null;
		reconnectAttempts.value = 0;
	};

	const onEvent = (event: string, data: Record<string, any>) => {
		if (event == "output") {
			output.value = data["output"] || "" + "";
			running_time.value = data["total_time"] || null;
		}
		if (event == "msg") {
			let name = nodeMap.value[data["Node"]] || { name: "", deep: 0 };
			if (data["Type"] == "start_node") {
				logItems.value.push({
					type: "node",
					name: name.name,
					action: t("start-exec"),
					deep: name.deep,
				});
				data["Node"] && (nodeStartTimes[data["Node"]] = Date.now());
			}
			if (data["Type"] == "finish_node") {
				let duration = 0;
				if (data["Node"] && nodeStartTimes[data["Node"]]) {
					duration = (Date.now() - nodeStartTimes[data["Node"]]) / 1000;
					delete nodeStartTimes[data["Node"]];
				}
				logItems.value.push({
					type: "node",
					name: name.name,
					action: t("stop-exec"),
					duration: duration.toFixed(2),
					deep: name.deep,
				});
			}
		}
		if (event == "error") {
			logItems.value.push({ type: "error", message: data["error"] });
			logGetError.value = true;
			isLogExpanded.value = true;
		}
		if (event == "agent") {
			logItems.value.push({ type: "agent", content: data["Content"] });
		}
		if (event == "stream") {
			const lastItem = logItems.value[logItems.value.length - 1];
			if (lastItem && lastItem.type === "stream") {
				// Update content
				const newContent = data["Content"] || "";
				lastItem.content = (lastItem.content || "") + newContent;
				lastItem.pendingChars = (lastItem.pendingChars || "") + newContent;

				// Update reasoning if present
				if (data["Reasoning"]) {
					const newReasoning = data["Reasoning"];
					lastItem.reasoning = (lastItem.reasoning || "") + newReasoning;
					lastItem.pendingReasoningChars =
						(lastItem.pendingReasoningChars || "") + newReasoning;
				}

				// Consolidate older content periodically to improve performance
				consolidateContent(lastItem);
			} else {
				// Create new stream item
				const newContent = data["Content"] || "";
				const newReasoning = data["Reasoning"];
				logItems.value.push({
					type: "stream",
					content: newContent,
					reasoning: newReasoning,
					pendingChars: newContent,
					pendingReasoningChars: newReasoning,
					consolidatedContent: "",
					consolidatedReasoning: "",
					lastConsolidationTime: Date.now(),
				});
			}
			// 触发重新渲染
			logItems.value = [...logItems.value];
		}
		nextTick(() => {
			if (logRef.value) {
				logRef.value.scrollTo({
					top: logRef.value.scrollHeight,
					behavior: "smooth",
				});
			}
		});
	};

	const validateInputs = () => {
		const validation = validateInputsUtil(input.value, inputMeta.value, t);
		invalidInputs.value = validation.invalidInputs;
		errorMessages.value = validation.errorMessages;
		return validation.isValid;
	};

	const processImages = async () => {
		// 进度回调函数
		const onProgress = (uploaded: number, total: number) => {
			// 更新上传进度
			logItems.value.push({
				type: "node",
				name: t("image-upload-progress"),
				action: t("uploaded-image-progress", {
					uploaded,
					total,
				}),
			});
		};

		// 使用公共模块处理图片
		const processedInput = await processImagesUtil(
			input.value,
			inputMeta.value,
			t,
			onProgress
		);
		// 更新本地输入值
		for (const [key, value] of Object.entries(processedInput)) {
			if (input.value[key] !== value) {
				input.value[key] = value;
			}
		}
		return processedInput;
	};

	const start = async () => {
		if (!hasOutputNode.value) {
			return;
		}
		if (!validateInputs()) {
			return;
		}

		try {
			isRunning.value = true;
			// 添加上传状态显示
			logItems.value.push({
				type: "node",
				name: t("image-upload-status"),
				action: t("start-upload"),
			});
			isLogExpanded.value = true;

			const processedInput = await processImages();

			// 上传完成提示
			logItems.value.push({
				type: "node",
				name: t("image-upload-status"),
				action: t("upload-success"),
			});

			if (prop.mode == "debug") {
				await debug(processedInput);
			}
			if (prop.mode == "run") {
				await run(processedInput);
			}
		} catch (error) {
			Error(t("error"), t("img_upload_err") + error);
			console.error(error);
			// 添加上传失败的日志
			logItems.value.push({ type: "error", message: t("img_upload_err") + error });
			isRunning.value = false;
		}
	};

	const debug = async (processedInput: Record<string, any>) => {
		let obj = currData || {};
		isLogExpanded.value = true;
		output.value = "";
		running_time.value = null;
		logItems.value = [];
		logGetError.value = false;

		// 停止之前的重连轮询（debug模式不需要重连）
		stopReconnectPolling();

		// 使用公共模块准备API输入
		const inputToSend = prepareInputForAPI(processedInput, inputMeta.value);

		try {
			await ProjectDebug(inputToSend, JSON.stringify(obj), onEvent);
		} catch (error) {
			Error(t("error"), t("debug_err") + error);
		} finally {
			handleRunComplete();
		}
	};

	const run = async (processedInput: Record<string, any>) => {
		isLogExpanded.value = true;
		output.value = "";
		running_time.value = null;
		logItems.value = [];
		logGetError.value = false;

		// 停止之前的重连轮询
		stopReconnectPolling();

		// 使用公共模块准备API输入
		const inputToSend = prepareInputForAPI(processedInput, inputMeta.value);

		try {
			const logId = await ProjectRuntime(prop.project_id, inputToSend, onEvent);

			// 如果获取到logId，启动断线重连轮询
			if (logId) {
				startReconnectPolling(logId);
			}
		} catch (error) {
			Error(t("error"), t("run_err") + error);

			// 网络错误时，如果有logId，尝试重连
			if (currentLogId.value) {
				logItems.value.push({
					type: "node",
					name: t("network-error"),
					action: t("attempting-reconnect"),
				});
				// 不调用handleRunComplete，让重连逻辑处理
			} else {
				handleRunComplete();
			}
		}
	};

	const getErrorMessage = (key: string) => {
		return errorMessages.value[key] || t("error");
	};

	const hasOutputNode = ref(false);

	type nodeNameMapValue = {
		name: string;
		deep: number;
	};
	const getNodeNameMap = (data: any, deep: number = 0) => {
		let nodes = data.nodes || [];
		let nodeNameMap: Record<string, nodeNameMapValue> = {};
		nodes.forEach((i: Node) => {
			nodeNameMap[i.id] = { name: i.data.name, deep: deep };
			if (i.data?.input?.workflow) {
				nodeNameMap = {
					...nodeNameMap,
					...getNodeNameMap(i.data.input.workflow, deep + 1),
				};
			}
		});
		return nodeNameMap;
	};
	const init = (data: any) => {
		// 使用公共模块解析工作流输入
		const { inputMeta: metaData, inputValues } = parseWorkflowInput(data);
		input.value = inputValues;
		inputMeta.value = metaData;

		// 检查是否有输出节点
		hasOutputNode.value = false;
		const nodes = data.nodes || [];
		nodes.forEach((i: Node) => {
			if (i.type == "out") {
				hasOutputNode.value = true;
			}
		});

		nodeMap.value = getNodeNameMap(data, 0);
		currData = data;
	};

	watch(
		() => prop.data,
		(data) => {
			init(data);
		}
	);

	onDeactivated(() => {
		if (controller) {
			// @ts-ignore
			controller.abort();
		}
		// 清理重连定时器
		stopReconnectPolling();
	});

	init(prop.data);

	const toggleLogExpand = () => {
		isLogExpanded.value = !isLogExpanded.value;
		if (isLogExpanded.value && logRef.value) {
			nextTick(() => {
				if (logRef.value) {
					logRef.value.scrollTop = logRef.value.scrollHeight;
				}
			});
		}
	};

	// Function to consolidate content to improve performance
	const consolidateContent = (item: LogItem) => {
		const now = Date.now();
		const CONSOLIDATION_INTERVAL = 500; // Consolidate every 500ms
		const MAX_PENDING_CHARS = 100; // Maximum number of characters to keep as individual spans

		// Check if it's time to consolidate
		if (
			!item.lastConsolidationTime ||
			now - item.lastConsolidationTime < CONSOLIDATION_INTERVAL
		) {
			return;
		}

		// Consolidate content if there are enough pending characters
		if (item.pendingChars && item.pendingChars.length > MAX_PENDING_CHARS) {
			// Keep the last MAX_PENDING_CHARS characters as pending, consolidate the rest
			const charsToConsolidate = item.pendingChars.slice(0, -MAX_PENDING_CHARS);
			item.consolidatedContent = (item.consolidatedContent || "") + charsToConsolidate;
			item.pendingChars = item.pendingChars.slice(-MAX_PENDING_CHARS);
		}

		// Consolidate reasoning if present
		if (
			item.pendingReasoningChars &&
			item.pendingReasoningChars.length > MAX_PENDING_CHARS
		) {
			const reasoningToConsolidate = item.pendingReasoningChars.slice(
				0,
				-MAX_PENDING_CHARS
			);
			item.consolidatedReasoning =
				(item.consolidatedReasoning || "") + reasoningToConsolidate;
			item.pendingReasoningChars = item.pendingReasoningChars.slice(-MAX_PENDING_CHARS);
		}

		// Update consolidation timestamp
		item.lastConsolidationTime = now;
	};

	// Consolidate all content when run completes
	const consolidateAllContent = () => {
		logItems.value.forEach((item) => {
			if (item.type === "stream") {
				// Consolidate all pending content
				if (item.pendingChars && item.pendingChars.length > 0) {
					item.consolidatedContent =
						(item.consolidatedContent || "") + item.pendingChars;
					item.pendingChars = "";
				}

				// Consolidate all pending reasoning
				if (item.pendingReasoningChars && item.pendingReasoningChars.length > 0) {
					item.consolidatedReasoning =
						(item.consolidatedReasoning || "") + item.pendingReasoningChars;
					item.pendingReasoningChars = "";
				}
			}
		});
	};

	const handleRunComplete = () => {
		isRunning.value = false;
		// 只有在没有错误且不在重连状态（或者是debug模式）时才收起日志
		if (!logGetError.value && (!isReconnecting.value || prop.mode === "debug")) {
			isLogExpanded.value = false;
		}
		// 停止重连轮询（debug模式下也会调用，但不会有重连状态）
		stopReconnectPolling();
		// Consolidate all content when run completes for maximum performance
		consolidateAllContent();
	};

	const handleImageError = (key: string, error: string) => {
		invalidInputs.value.push(key);
		errorMessages.value[key] = error;
	};

	const clearImageError = (key: string) => {
		invalidInputs.value = invalidInputs.value.filter((k) => k !== key);
		delete errorMessages.value[key];
	};
	const copyOutput = () => {
		if (output.value) {
			navigator.clipboard.writeText(output.value);
			Success(t("copy_success"), t("copied_to_clipboard"));
		}
	};
</script>

<style scoped>
	.fade-in-item {
		animation: fadeIn 0.5s ease-in-out forwards;
	}

	.fade-in-char {
		opacity: 0;
		animation: fadeIn 0.3s ease-in-out forwards;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}

	.text-red-500 {
		@apply bg-red-50 border border-red-200 rounded-md px-2 py-1 transition-all duration-300 ease-in-out;
	}

	/* 新增样式 */
	.bg-blue-50 {
		@apply transition-all duration-300 ease-in-out;
	}

	.bg-blue-50:hover {
		@apply bg-blue-100;
	}

	.text-blue-700 {
		@apply leading-relaxed;
	}
</style>

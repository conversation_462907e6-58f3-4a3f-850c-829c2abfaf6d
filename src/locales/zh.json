{"nodes": {"group": {"base": "基础", "data_processing": "数据处理", "data_acquisition": "数据获取", "ai_model": "AI模型", "tool": "工具", "plugin": "插件"}, "in": {"label": "开始（输入）", "nodeShowName": "开始", "helpDoc": "https://flowai.cc/tutorial/input/"}, "out": {"label": "结束（输出）", "nodeShowName": "结束", "helpDoc": "https://flowai.cc/tutorial/output/"}, "browser": {"label": "网页内容抓取", "nodeShowName": "网页内容抓取", "helpDoc": "https://flowai.cc/tutorial/web-scraper/", "body": "网页内容"}, "llm": {"label": "LLM", "nodeShowName": "LLM", "helpDoc": "https://flowai.cc/tutorial/llm/", "llm_output_desc": "大语言模型的输出结果"}, "llm_cls": {"label": "LLM意图分类", "nodeShowName": "LLM意图分类", "helpDoc": "https://flowai.cc/tutorial/llm-intent/"}, "template": {"label": "内容拼接器", "nodeShowName": "内容拼接器", "helpDoc": "https://flowai.cc/tutorial/template/", "template_output_desc": "拼接后的数据"}, "adv_template": {"label": "高级模板", "nodeShowName": "高级模板", "helpDoc": "https://flowai.cc/tutorial/advance-template/", "template_output_desc": "模板输出"}, "http": {"label": "HTTP请求", "nodeShowName": "HTTP请求", "helpDoc": "https://flowai.cc/tutorial/http/", "output_body_desc": "网页内容", "output_status_desc": "状态码"}, "current_time": {"label": "当前时间", "nodeShowName": "当前时间", "helpDoc": "https://flowai.cc/tutorial/time/", "output_desc": "返回系统当前时间，格式类似于：2024-01-02 03:04:05"}, "plugin": {"label": "自定义插件", "nodeShowName": "自定义插件", "group": "插件"}, "code_runner": {"label": "代码执行器", "nodeShowName": "代码执行器", "helpDoc": "https://flowai.cc/tutorial/code-runner/", "output_desc": "代码执行结果"}, "json_param_extract": {"label": "JSON参数提取", "nodeShowName": "JSON参数提取", "helpDoc": "https://flowai.cc/tutorial/json-extract/", "output_desc": "提取的JSON参数"}, "condition": {"label": "条件判断", "nodeShowName": "条件判断", "helpDoc": "https://flowai.cc/tutorial/condition/"}, "condition_class": {"label": "条件分类", "nodeShowName": "条件分类", "output_desc": "分类的信息"}, "loop": {"label": "循环", "nodeShowName": "循环", "helpDoc": "", "output_desc": "循环执行结果, JSON 数组"}, "loop_start": {"label": "循环开始", "nodeShowName": "循环开始", "helpDoc": "", "loop_start_item_desc": "循环项", "loop_start_index_desc": "循环索引"}, "loop_end": {"label": "循环结束", "nodeShowName": "循环结束", "helpDoc": "", "loop_end_export_var_desc": "$循环开始.item"}, "json_pretty": {"label": "JSON格式化", "nodeShowName": "JSON格式化", "helpDoc": "https://flowai.cc/tutorial/json-format/", "output_desc": "格式化后的JSON"}, "llm_agent": {"label": "LLMAgent", "nodeShowName": "LLM Agent", "helpDoc": "https://flowai.cc/tutorial/llm-agent/", "output_desc": "LLM Agent的输出结果"}}, "condition_types": {"equals": "等于", "not_equals": "不等于", "contains": "包含", "not_contains": "不包含", "starts_with": "开头等于", "not_starts_with": "开头不等于", "ends_with": "结尾等于", "not_ends_with": "结尾不等于"}, "personal_settings": "个人设置", "settings": "设置", "custom_llm": "自定义LLM", "credits": "积分", "current_credits": "当前积分", "user_info": "用户信息", "user_identifier": "用户标识", "email": "邮箱", "enter_email": "请输入邮箱", "email_function_coming_soon": "邮箱修改功能即将推出", "phone": "电话", "enter_phone": "请输入电话", "new_password": "新密码", "enter_new_password": "请输入新密码", "old_password": "旧密码", "enter_old_password": "输入旧密码", "save_settings": "保存设置", "no_changes": "没有修改任何内容", "old_password_required": "修改密码时，必须输入旧密码", "update_success": "更新成功", "update_failed": "更新失败", "existing_custom_llm": "现有自定义LLM", "edit_custom_llm": "编辑自定义LLM", "model_name": "模型名称", "model_protocol": "模型调用协议", "model_endpoint": "模型端点", "model_key": "模型密钥", "support_function_call": "支持函数调用", "support_json_output": "支持JSON输出", "support_vision": "支持视觉", "add_custom_llm": "添加自定义LLM", "coming_soon": "即将推出", "stay_tuned": "敬请期待", "usernameOrEmail": "用户名/邮箱", "login_password": "登录密码", "recharge": "充值", "or": "或", "login": "登录", "login_with_google": "使用Google登录", "login_with_github": "使用Github登录", "error": "错误", "create-copy-failed": "创建副本失败", "delete-failed": "删除失败", "operation-failed": "操作失败", "enter_username_or_email_and_password": "请输入用户名/电子邮箱和密码", "login_error": "登录失败：", "google-login-error": "Google登录失败：", "github-login-error": "Github登录失败：", "login-comming-soon": "注册功能即将开放，敬请期待！", "create-workflow": "新建工作流", "run": "运行", "edit": "编辑", "searching": "正在搜索...", "no-search-result": "没有找到相关结果", "search-workflow": "搜索工作流", "workflow": "工作流", "run_logs": "运行日志", "logout": "退出登录", "not-set": "未设置", "user": "用户", "confirm-logout": "确认退出登陆？", "create-copy": "创建副本", "delete": "删除", "confirm-delete-project": "确定要删除该项目吗？", "example": "示例", "create-blank-workflow": "创建空白工作流", "or-start-from-example": "或者从以下示例开始", "click-button-to-start-automation": "点击下面按钮，开始您的自动化之旅", "no-projects": "还没有项目呢", "unnamed-project": "未命名项目", "no-description": "暂无描述", "use-this-example": "使用此示例", "logic-flow": "逻辑编排", "visual-logic-flow": "可视化逻辑编排，实现复杂业务流程自动化。", "multi-llm-assistant": "多LLM智能助手", "integrate-multiple-llm-models": "集成多个LLM模型，实现智能决策和内容生成。", "llm-data-processing": "LLM数据处理", "llm-data-processing-description": "使用LLM进行数据清洗、转换和分析。", "confirm-delete-log": "确定删除此日志？", "delete-log-failed": "删除日志失败", "run-logs": "运行日志", "show-logs-in-last-15-days": "仅显示最近 15 天内的记录", "no-logs": "暂无日志", "view": "查看", "close": "关闭", "loading": "加载中...", "success": "成功", "return": "返回", "save": "保存", "debug": "调试", "content-not-saved-leave": "内容未保存，是否离开？", "save-error": "保存失败", "save-success": "保存成功", "loading-error": "加载失败", "current-workflow-has-no-output-node-confirm": "当前工作流没有输出节点,确定要保存吗?", "project-name-cannot-be-empty": "项目名称不能为空", "project-name": "项目名称：", "project-description": "项目描述：", "none": "无", "delete-node": "删除节点", "node-config": "节点配置", "help-doc": "帮助文档", "copy": "复制", "paste": "粘贴", "undo": "撤回", "start": "开始", "runtime": "运行器", "copy_success": "复制成功", "copied_to_clipboard": "已复制到剪贴板", "input": "输入", "output": "输出", "seconds": "秒", "copy_output": "复制输出", "input_value": "输入值", "use_variable_with_slash": "可以通过输入<span class=\"bg-base-200 px-1 py-0.5 rounded\">/</span>使用变量。", "no_input_data": "- 无输入数据 -", "collapse": "收起", "expand": "展开", "node_name_cannot_be_empty": "节点名称不能为空，请重新输入", "node_name_already_exists": "节点名称\"{name}\"已经存在，请重新输入", "search": "搜索...", "upload-success": "上传完成", "stop-exec": "结束执行", "start-exec": "开始运行", "pls-select": "请选择", "warnning-no-output-node": "警告: 当前工作流没有\"输出（结束）\"节点，无法正常运行。", "duration": "耗时", "running_time": "运行时间", "pls-select-valid-option": "请选择一个有效选项", "pls-select-at-least-one-option": "请至少选择一个选项", "pls-upload-image": "请上传图片", "max-upload-image": "最多上传 {count} 张图片", "image-upload-progress": "图片上传进度", "uploaded-image-progress": "已上传 {uploadedImages}/{totalImages} 张图片", "image-upload-status": "图片上传状态", "start-upload": "开始上传", "img_upload_err": "处理图片上传失败:", "debug_err": "调试出错：", "run_err": "运行出错：", "preview-img": "预览图片", "support-jpg-png-gif-format": "支持 jpg、png、gif 格式，单个文件不超过 5MB", "continue-upload": "继续上传", "remaining-upload-count": "(还可上传 {count} 张)", "click-upload": "点击上传", "drag-image-here": "拖拽图片到此处或", "upload-invalid-image-format": "请上传 jpg、png 或 gif 格式的图片", "image-size-exceeds-limit": "图片大小不能超过 5MB", "image-processing-failed": "图片处理失败，请重试", "workflow-edit": "工作流编辑", "runtime-workflow": "运行工作流", "text": "文本", "longtext": "长文本", "select": "下拉框", "radio": "单选框", "image": "图片", "checkbox": "多选框", "type": "类型", "name": "名称", "default": "默认", "node_name": "节点名称", "add_input_variable": "可添加允许用户输入的变量", "input_variable": "输入变量", "options": "选项", "add_option": "新增选项", "add_new_variable": "添加新变量", "default_value": "默认值", "default_1_image": "默认1张", "allow_upload_image_number": "允许上传图片数量", "drag_to_reorder": "拖动以重新排序", "option_value": "选项值", "at_least_one_option": "至少需要保留一个选项", "browser_url": "网页抓取的地址", "from_variable": "来自变量", "direct_input": "直接输入", "input_url": "输入网址", "code_runner_desc": "*目前只支持运行 JavaScript 代码，代码会运行在<b>无联网环境的 Node 18 环境</b \t\t\t\t>下。执行时间最长不超过30秒(包括容器构建时间)。", "get_from_context": "从上下文获取", "manual_input": "手动输入", "variable_name": "变量名", "input_params": "输入参数", "add": "添加", "js_code": "JS代码", "key_must_be_string": "键名必须是字符串类型。", "invalid_variable_name": "无效的变量名。请使用字母、数字和下划线，且不能以数字开头。", "select_condition_content": "选择判断的内容", "set_condition": "设置条件判断", "condition_options": "条件选项", "please_add_condition_options": "请添加条件选项", "add_condition": "添加条件", "content": "内容", "condition_content": "判断内容", "request_body": "请求体", "request_headers_empty": "请求头为空，如果需要添加请求头，请点击上面的按钮", "request_headers": "请求头", "http_address": "HTTP 地址", "or_the_first_element_of_the_array": "或者数组的第一个元素", "for_example": "比如", "json_data_source": "JSON数据源", "json_path": "JSON路径：", "pls-select-json-data-source": "请选择数据源", "model": "模型", "context_image": "上下文图片", "not_use_image": "不使用图片", "prompt": "提示词", "system_prompt": "系统提示词", "more_config": "更多配置", "json_output": "JSON输出", "enable_json_output_warning": "开启JSON输出后，你需要在提示词中，明确要求LLM输出JSON格式数据", "max_output_length": "最大输出长度", "max_output_length_warning": "控制模型输出的最大token数量，默认为0表示不限制", "set_llm_classifier_class": "设置LLM的分类类别", "select_judgment_content": "选择判断的内容", "classifier_class": "分类的类别", "please_add_class": "请添加类别", "class_name": "类别名称", "class": "类别", "can_be_empty": "可以为空，如果为空，则使用默认的分类提示词", "classifier_prompt": "分类提示词", "can_use_variables": "可以使用变量，如：<span class=\"bg-base-200 px-1 py-0.5 rounded\">$输入.input</span>", "loop": "循环", "loop_var": "循环变量", "please_input_node_name": "请输入节点名称", "please_select_loop_var": "请选择循环变量", "loop_var_desc": "需要循环的变量，必须是数组类型(只要能被JSON解析为数组即可)，例如：[\"a\", \"b\", \"c\"]，循环次数为数组长度。如果不是数组类型，比如变量内容是\"a\"，则会以[\"a\"]看待，循环只会执行一次。<span class=\"font-bold\" >循环最多执行500次，如果循环次数超过500次，则循环会停止。</span>", "loop_sleep": "循环间隔", "loop_sleep_desc": "单位：秒，默认0秒表示一个循环结束立即进行下一个循环，中间没有等待。如果设置大于0，则表示在2个循环之间会进行休眠等待，防止高频请求。", "no_output": "暂无输出", "no_results_yet": "暂无运行结果", "click_start_to_run": "点击开始按钮运行流程", "export_var": "导出变量", "please_select_export_var": "请选择要导出的变量", "end_loop_once": "结束循环一次", "export_content": "导出内容", "start_loop_once": "开始循环一次", "each_element_will_execute_once": "每一个元素会执行一次", "loop_start": "循环开始", "loop_end": "循环结束", "select_output_content": "选择输出内容", "custom_plugin": "自定义插件", "return_system_time": "返回执行的系统时间", "failed_to_create_canvas_context": "无法创建 canvas context", "failed_to_compress_image": "图片压缩失败", "failed_to_load_image": "图片加载失败", "failed_to_upload_file": "文件上传失败", "add_custom_llm_success": "添加自定义LLM成功", "add_custom_llm_error": "添加自定义LLM失败", "confirm_delete_custom_llm": "确定要删除这个自定义LLM吗？", "delete_custom_llm_success": "删除自定义LLM成功", "delete_custom_llm_error": "删除自定义LLM失败", "get_custom_llm_error": "获取自定义LLM失败", "update_custom_llm_success": "更新自定义LLM成功", "update_custom_llm_error": "更新自定义LLM失败", "enter_model_key": "输入模型API KEY", "save_edit": "保存编辑", "cancel": "取消", "endpoint_placeholder": "输入API地址，比如 https://api.openai.com/v1", "support_model_type_desc": "请注意，目前我们只支持OpenAI协议、Anthropic协议、与OpenAI兼容的协议（如deepseek)。", "must_enter_old_password": "修改密码时，必须输入旧密码", "update_error": "更新失败", "no_variable": "没有变量", "click_add_to_start": "点击\"添加\"开始设置变量", "go_template_example": "<li> 变量: <code>&#123;&#123; .varName &#125;&#125;</code></li><li>条件: <code>&#123;&#123;if .condition&#125;&#125;...&#123;&#123;else&#125;&#125;...&#123;&#123; end &#125;&#125;</code></li><li>循环: <code>&#123;&#123;range .items&#125;&#125;...&#123;&#123; end &#125;&#125;</code></li><li>管道: <code>&#123;&#123; .value | upper | lower &#125;&#125;</code></li><li> 函数: <code>&#123;&#123; json_parse .value &#125;&#125;</code></li>", "go_template_example_title": "Go Template 语法示例:", "variable": "变量", "condition": "条件", "pipe": "管道", "function": "函数", "recharge_confirmation": "充值确认", "points_to_add": "积分数", "price": "价格", "confirm_payment": "前往付款", "payment_error": "充值失败，请稍后再试", "redirecting_to_payment": "正在跳转支付页...", "context_limit": "上下文大小", "context_limit_description": "LLM 的上下文大小，单位K", "publish_api": "发布API", "versions": "版本", "no-published-versions": "暂时没有发布的版本", "publish-workflow": "发布工作流", "confirm-delete-version": "确认删除版本", "delete-version-error": "删除版本错误", "publish-error": "发布工作流错误", "publish-success": "发布工作流成功", "published-versions": "已发布的版本", "publish-current-version": "发布当前版本", "delete-version-success": "删除版本成功", "failed-to-load-published-version": "读取版本错误", "api_management": "API 管理", "api_keys": "API 密钥", "create_new_key": "创建新密钥", "key": "密钥", "created_at": "创建时间", "expires_at": "过期时间", "last_used": "最后使用时间", "actions": "操作", "no_api_keys": "暂无 API 密钥", "never": "从未使用", "published_workflows": "已发布的工作流", "version": "版本", "description": "描述", "status": "状态", "update_status": "更新状态", "create_new_api_key": "创建新 API 密钥", "key_name": "密钥名称", "enter_key_name": "请输入密钥名称", "expires_in_days": "有效期（天）", "creating": "创建中", "create": "创建", "api_key_created": "API 密钥已创建", "key_warning": "请注意：此密钥可以直接执行你的workflow，请妥善保存！", "api_key": "API 密钥", "done": "完成", "update_workflow_status": "更新工作流状态", "current_status": "当前状态", "new_status": "新状态", "updating": "更新中", "update": "更新", "no_published_workflows": "暂无已发布的工作流", "confirm_delete": "确认删除", "confirm_delete_api_key": "确定要删除这个 API 密钥吗？此操作不可撤销。", "confirm_delete_workflow": "确定要删除这个已发布的工作流吗？此操作不可撤销。", "confirm": "确认", "save-and-publish-current-workflow": "保存并发布当前工作流", "publish-warning-message": "注意：此操作将保存当前工作流并将其发布为最新版本。", "confirm-save-and-publish": "确定要保存并发布当前工作流吗？当前版本将被保存并覆盖为最新版。", "copy_to_clipboard_failed": "复制到剪贴板错误，请你检查权限", "endpoint": "端点", "workflow_id": "工作流ID", "api_request_example": "API 请求例子", "signup_msg": "还没有账号？", "signup": "注册账号", "login_msg": "已有账号？", "signup_with_google": "使用Google注册", "signup_with_github": "使用Github注册", "register_success": "注册成功", "register_error": "注册失败", "please_fill_all_fields": "请填写所有字段", "password_not_match": "两次密码输入不一致", "invalid_email": "请输入有效的邮箱地址", "email_verification": "邮箱验证", "verification_code": "验证码", "enter_verification_code": "请输入验证码", "verification_code_sent_to": "验证码已发送至", "verify_email": "验证邮箱", "verification_success": "验证成功", "verification_error": "验证失败", "email_verified": "邮箱已验证", "didnt_receive_code": "没有收到验证码？", "resend_code": "重新发送", "resend_success": "重新发送成功", "resend_error": "重新发送失败", "back_to_login": "返回登录", "please_enter_verification_code": "请输入验证码", "verification_email_sent": "验证邮件已发送，请查收", "confirm_password": "确认密码", "credits_reward_info": "积分奖励信息", "email_signup_credits": "邮箱注册：赠送 {credits} 积分", "oauth_signup_credits": "通过Google或Github登录：赠送 {credits} 积分", "auto_verifying": "正在自动验证您的邮箱...", "output_lang": "输出语言", "built_in_tools": "内置工具", "max_iterations": "最大迭代次数", "max_iterations_desc": "LLM Agent 的最大迭代次数，超过此次数将停止执行", "advanced_settings": "高级设置", "mcp_servers": "MCP 服务器", "unnamed_server": "未命名服务器", "server_name": "服务器名称", "server_url": "服务器地址", "server_type": "服务器类型", "server_headers": "自定义HTTP头", "add_header": "添加HTTP头", "no_mcp_servers": "暂无 MCP 服务器配置", "add_mcp_server": "添加 MCP 服务器", "fetch_web": "网页抓取", "http_call": "HTTP请求", "calculator": "计算器", "custom_llm_management": "自定义LLM管理", "manage_your_custom_llm_models": "管理您的自定义LLM模型", "no_custom_llms": "暂无自定义LLM", "no_custom_llms_desc": "您还没有添加任何自定义LLM模型，点击上方按钮开始添加。", "basic_info": "基本信息", "advanced_options": "高级选项", "tokens": "令牌", "function_call": "函数调用", "vision": "视觉", "editing": "编辑中", "confirm_delete_custom_llm_message": "确定要删除模型 \"{name}\" 吗？", "delete_warning_irreversible": "此操作不可撤销，删除后将无法恢复。", "configure_new_llm_model": "配置新的LLM模型", "modify_llm_configuration": "修改LLM配置", "no_special_features": "无特殊功能", "confirm_delete_item_message": "确定要删除 \"{name}\" 吗？", "confirm_delete_default_message": "确定要删除此项目吗？", "editor": {"fullscreen": "全屏编辑", "title": "编辑器", "close": "关闭"}}